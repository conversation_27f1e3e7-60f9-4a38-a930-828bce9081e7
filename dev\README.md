# 低延迟实时语音识别系统

基于faster-whisper的高效实时语音转文本系统，专为低延迟场景优化。

## 特点

- ⚡ **低延迟**: 0.5秒音频块处理，快速响应
- 🎯 **高准确率**: 基于Whisper large-v3模型
- 🔄 **实时处理**: 自动语音活动检测(VAD)
- 💡 **简洁易用**: 单文件实现，最少代码
- 🛠️ **高效优化**: 预加载模型，优化参数配置

## 快速开始

### 1. 安装依赖

```bash
# 自动安装依赖
python install_stt_deps.py

# 或手动安装
pip install faster-whisper sounddevice soundfile numpy
```

### 2. 运行程序

```bash
python realtime_stt.py
```

### 3. 使用方法

1. 程序启动后会自动初始化模型（首次可能需要几秒钟）
2. 看到"🎤 开始实时语音识别..."提示后开始说话
3. 程序会自动检测语音开始和结束
4. 识别结果会实时显示
5. 按 Enter 键停止识别
6. 按 Ctrl+C 退出程序

## 技术特性

### 低延迟优化

- **音频块大小**: 0.5秒（平衡延迟和准确性）
- **VAD检测**: 快速语音活动检测
- **模型参数**: beam_size=1, temperature=0.0（最快速度）
- **预加载**: 避免首次识别延迟

### 配置参数

```python
# 音频配置
chunk_duration: 0.5秒    # 音频块大小
silence_duration: 1.0秒  # 静音检测时长
sample_rate: 16000Hz     # 采样率

# VAD配置
min_speech_duration: 0.3秒  # 最小语音时长
min_silence_duration: 0.8秒 # 最小静音时长
```

## 系统要求

- Python 3.8+
- 麦克风设备
- 模型路径: `D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3`
- 内存: 建议4GB+（模型加载）

## 故障排除

### 常见问题

1. **模型路径错误**
   - 确认模型文件存在于指定路径
   - 检查路径权限

2. **音频设备问题**
   - 确认麦克风正常工作
   - 检查音频设备权限

3. **依赖安装失败**
   - 使用管理员权限运行
   - 更新pip: `pip install --upgrade pip`

### 性能优化

- 使用GPU加速（如果可用）
- 调整音频块大小平衡延迟和准确性
- 根据硬件性能调整模型参数

## 代码结构

```
dev/
├── realtime_stt.py      # 主程序（核心实现）
├── install_stt_deps.py  # 依赖安装脚本
└── README.md           # 使用说明
```

## 技术架构

- **WhisperClient**: 语音识别核心
- **AudioRecorder**: 音频录制和VAD
- **RealTimeSTT**: 实时处理协调器

## 许可证

本项目基于现有的LLM对话系统框架开发。
