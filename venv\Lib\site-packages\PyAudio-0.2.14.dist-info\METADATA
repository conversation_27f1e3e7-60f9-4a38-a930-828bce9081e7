Metadata-Version: 2.1
Name: PyAudio
Version: 0.2.14
Summary: Cross-platform audio I/O with PortAudio
Home-page: https://people.csail.mit.edu/hubert/pyaudio/
Author: <PERSON>
License: MIT
Classifier: Development Status :: 4 - Beta
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Multimedia :: Sound/Audio
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Provides-Extra: test
Requires-Dist: numpy ; extra == 'test'

<img align="right" width="200" style="margin-left: 3px" src="https://people.csail.mit.edu/hubert/pyaudio/images/snake-300.png">

# PyAudio

PyAudio provides Python bindings for PortAudio v19, the cross-platform audio I/O library. With PyAudio, you can easily use Python to play and record audio on a variety of platforms, such as GNU/Linux, Microsoft Windows, and Apple macOS.

PyAudio is distributed under the MIT License.

* [Homepage](https://people.csail.mit.edu/hubert/pyaudio/)
* [API Documentation](https://people.csail.mit.edu/hubert/pyaudio/docs/)
* [PyPi](https://pypi.python.org/pypi/PyAudio)

## Installation

See the INSTALLATION file in the source distribution for details. In summary, install PyAudio using `pip` on most platforms.

### Windows

```sh
python -m pip install pyaudio
```

This installs the precompiled PyAudio library with PortAudio v19 19.7.0 included. The library is compiled with support for Windows MME API, DirectSound, WASAPI, and WDM-KS. It does not include support for ASIO. If you require support for APIs not included, you will need to compile PortAudio and PyAudio.

### macOS

Use [Homebrew](https://brew.sh) to install the prerequisite [portaudio](http://portaudio.com) library, then install PyAudio using `pip`:

```sh
brew install portaudio
pip install pyaudio
```

### GNU/Linux

Use the package manager to install PyAudio. For example, on Debian-based systems:

```sh
sudo apt install python3-pyaudio
```

Alternatively, if the latest version of PyAudio is not available, install it using `pip`. Be sure to first install development libraries for `portaudio19` and `python3`.

### Building from source

See the INSTALLATION file.

## Documentation & Usage Examples

* Read the [API Documentation](https://people.csail.mit.edu/hubert/pyaudio/docs/), or generate it from the source using [`sphinx`](https://www.sphinx-doc.org/).

* Usage examples are in the `examples` directory of the source distribution, or see the [project homepage](https://people.csail.mit.edu/hubert/pyaudio/).

## License

PyAudio is distributed under the MIT License. See LICENSE.txt.
