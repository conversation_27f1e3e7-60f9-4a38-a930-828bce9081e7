#!/usr/bin/env python3
"""
语音识别演示脚本
展示简化和详细两种显示模式
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from low_latency_stt import LowLatencySTT
import time

def demo_simple_mode():
    """演示简化模式"""
    print("=== 简化模式演示 ===")
    print("特点：最少的终端输出，只显示识别结果")
    print()
    
    model_path = r"D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478"
    
    # 创建STT实例（默认简化模式）
    stt = LowLatencySTT(model_path, verbose=False)
    
    try:
        stt.start()
        print("开始说话测试...")
        
        # 运行30秒
        time.sleep(30)
        
    except KeyboardInterrupt:
        pass
    finally:
        stt.stop()

def demo_verbose_mode():
    """演示详细模式"""
    print("=== 详细模式演示 ===")
    print("特点：显示详细的状态信息和时间统计")
    print()
    
    model_path = r"D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478"
    
    # 创建STT实例（详细模式）
    stt = LowLatencySTT(model_path, verbose=True)
    
    try:
        stt.start()
        print("开始说话测试...")
        
        # 运行30秒
        time.sleep(30)
        
    except KeyboardInterrupt:
        pass
    finally:
        stt.stop()

def main():
    """主函数"""
    print("语音识别显示模式对比演示")
    print("1. 简化模式 - 最少输出")
    print("2. 详细模式 - 完整信息")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择模式 (1-3): ").strip()
        
        if choice == "1":
            demo_simple_mode()
        elif choice == "2":
            demo_verbose_mode()
        elif choice == "3":
            print("退出演示")
            break
        else:
            print("无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
