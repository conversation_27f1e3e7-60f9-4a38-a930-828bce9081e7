#!/usr/bin/env python3
"""
低延迟语音输入示例程序
使用faster-whisper模型和pyaudio实现实时语音识别
"""

import pyaudio
import numpy as np
import threading
import time
from faster_whisper import WhisperModel
from collections import deque
import queue
import torch

class LowLatencySTT:
    def __init__(self, model_path: str):
        """初始化低延迟STT系统"""
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paFloat32
        
        # 语音检测参数
        self.silence_threshold = 0.1
        self.min_speech_duration = 0.5  # 最小语音时长(秒)
        self.max_silence_duration = 1.0  # 最大静音时长(秒)
        
        # 检查GPU可用性并设置设备
        if torch.cuda.is_available():
            device = "cuda"
            compute_type = "float16"
            print(f"使用GPU加速: {torch.cuda.get_device_name()}")
        else:
            device = "cpu"
            compute_type = "int8"
            print("使用CPU模式")

        # 初始化组件
        self.audio = pyaudio.PyAudio()
        self.model = WhisperModel(
            model_path,
            device=device,
            compute_type=compute_type,
            num_workers=1,  # 减少内存使用
            download_root=None,
            local_files_only=True
        )

        # 状态控制
        self.is_running = False
        self.audio_queue = queue.Queue()
        self.result_callback = None

        print("模型加载完成")

    def _calculate_rms(self, audio_data: np.ndarray) -> float:
        """计算音频RMS能量"""
        return np.sqrt(np.mean(audio_data ** 2))

    def _is_speech(self, audio_data: np.ndarray) -> bool:
        """简单语音活动检测"""
        return self._calculate_rms(audio_data) > self.silence_threshold

    def _audio_callback(self, in_data, _frame_count, _time_info, _status):
        """音频输入回调函数"""
        audio_data = np.frombuffer(in_data, dtype=np.float32)
        self.audio_queue.put(audio_data)
        return (None, pyaudio.paContinue)

    def _process_audio(self):
        """音频处理线程"""
        speech_buffer = deque()
        silence_counter = 0
        speech_detected = False
        
        while self.is_running:
            try:
                # 获取音频数据
                audio_chunk = self.audio_queue.get(timeout=0.1)
                
                # 语音活动检测
                is_speech = self._is_speech(audio_chunk)
                
                if is_speech:
                    speech_buffer.append(audio_chunk)
                    silence_counter = 0
                    if not speech_detected:
                        speech_detected = True
                        print("🎤 语音中...")
                else:
                    silence_counter += 1
                    # 在语音后添加少量静音数据
                    if speech_detected and silence_counter < 10:
                        speech_buffer.append(audio_chunk)
                
                # 检查是否需要处理语音
                if speech_detected and silence_counter > int(self.max_silence_duration * self.sample_rate / self.chunk_size):
                    if len(speech_buffer) > int(self.min_speech_duration * self.sample_rate / self.chunk_size):
                        # 合并音频数据并识别
                        audio_data = np.concatenate(list(speech_buffer))
                        self._transcribe_audio(audio_data)
                    
                    # 重置状态
                    speech_buffer.clear()
                    speech_detected = False
                    silence_counter = 0
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"音频处理错误: {e}")

    def _transcribe_audio(self, audio_data: np.ndarray):
        """转录音频数据"""
        try:
            start_time = time.time()
            
            # 使用优化参数进行快速识别
            segments, _ = self.model.transcribe(
                audio_data,
                language="zh",
                beam_size=1,  # 最小beam size以获得最快速度
                temperature=0.0,  # 确定性输出
                no_speech_threshold=0.6,
                condition_on_previous_text=False,  # 不依赖上下文，提升速度
                without_timestamps=True,  # 不生成时间戳，提升速度
                initial_prompt="以下是普通话的句子。",
                vad_filter=True,  # 启用VAD过滤
                vad_parameters=dict(min_silence_duration_ms=500)  # VAD参数优化
            )
            
            # 提取文本
            text = "".join(segment.text for segment in segments).strip()
            
            if text:
                elapsed = time.time() - start_time
                print(f"识别: {text} ({elapsed:.1f}s)")

                # 调用回调函数
                if self.result_callback:
                    self.result_callback(text)
                
        except Exception as e:
            print(f"识别失败: {e}")

    def start(self, result_callback=None):
        """开始语音识别"""
        self.result_callback = result_callback
        self.is_running = True
        
        # 启动音频流
        self.stream = self.audio.open(
            format=self.format,
            channels=self.channels,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )
        
        # 启动处理线程
        self.process_thread = threading.Thread(target=self._process_audio, daemon=True)
        self.process_thread.start()
        
        self.stream.start_stream()
        print("开始语音识别 (Ctrl+C 停止)")

    def stop(self):
        """停止语音识别"""
        self.is_running = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        
        self.audio.terminate()
        print("语音识别已停止")

def check_gpu_info():
    """检查GPU信息"""
    if torch.cuda.is_available():
        current_gpu = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_gpu)
        gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
        print(f"GPU信息: {gpu_name} ({gpu_memory:.1f}GB)")
        print(f"CUDA版本: {torch.version.cuda}")
    else:
        print("未检测到CUDA GPU")

def main():
    """主函数"""
    # 显示GPU信息
    check_gpu_info()

    model_path = r"D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478"

    # 创建STT实例
    stt = LowLatencySTT(model_path)
    
    # 定义结果处理函数
    def on_result(_text):
        pass  # 结果已在识别时显示
    
    try:
        # 开始识别
        stt.start(result_callback=on_result)
        
        # 保持运行
        while True:
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n正在停止...")
        stt.stop()

if __name__ == "__main__":
    main()
