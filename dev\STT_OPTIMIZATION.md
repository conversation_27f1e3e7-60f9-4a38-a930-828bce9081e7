# 语音识别系统显示优化

## 优化内容

### 1. 显示模式控制
- 添加了 `verbose` 参数控制显示详细程度
- 默认为简化模式，可通过命令行参数 `--verbose` 启用详细模式

### 2. 简化模式特点
- **初始化**: 只显示 "STT系统初始化完成"
- **语音检测**: 用简单的点 `.` 表示检测到语音
- **识别过程**: 用 `>` 提示开始识别
- **识别结果**: 直接显示文本，无额外装饰
- **错误处理**: 用简单的 `E` 表示错误
- **启动信息**: 一行简洁提示

### 3. 详细模式特点
- 保留原有的详细状态信息
- 显示模型路径、识别耗时等详细信息
- 适合调试和开发使用

## 使用方法

### 命令行使用
```bash
# 简化模式（默认）
python low_latency_stt.py

# 详细模式
python low_latency_stt.py --verbose

# 指定模型路径
python low_latency_stt.py --model /path/to/model --verbose
```

### 代码中使用
```python
from low_latency_stt import LowLatencySTT

# 简化模式
stt = LowLatencySTT(model_path, verbose=False)

# 详细模式
stt = LowLatencySTT(model_path, verbose=True)
```

## 显示对比

### 简化模式输出示例
```
STT系统初始化完成
语音识别已启动 (Ctrl+C 停止)
.
> 你好世界
.
> 这是一个测试
停止中...
已停止
```

### 详细模式输出示例
```
模型加载完成: edaa852ec7e145841d8ffdb056a99866b5f0a478
开始实时语音识别...
说话后停顿1秒即可获得识别结果
按 Ctrl+C 停止
检测到语音...
正在识别...
识别结果: 你好世界 (耗时: 0.85s)
检测到语音...
正在识别...
识别结果: 这是一个测试 (耗时: 0.92s)
正在停止...
语音识别已停止
```

## 进一步优化建议

### 1. 性能优化
- 使用更小的模型（如 base 或 small）减少延迟
- 调整 `chunk_size` 和缓冲区大小
- 实现模型预热机制

### 2. 用户体验优化
- 添加音量指示器
- 实现可配置的静音检测阈值
- 添加识别置信度显示

### 3. 功能扩展
- 支持多语言切换
- 添加关键词唤醒功能
- 实现语音命令识别

### 4. 系统集成
- 添加日志记录功能
- 支持配置文件
- 实现 WebSocket 接口用于远程调用

## 演示脚本

运行 `stt_demo.py` 可以对比两种显示模式的效果：

```bash
python stt_demo.py
```

该脚本提供交互式菜单，可以分别体验简化模式和详细模式的显示效果。
