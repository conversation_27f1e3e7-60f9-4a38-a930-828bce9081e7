#!/usr/bin/env python3
"""
高级语音块处理示例
包含多种优秀的语音处理技术
"""

import numpy as np
import webrtcvad
import scipy.signal
from collections import deque
import librosa

class AdvancedSpeechProcessor:
    def __init__(self, sample_rate=16000):
        self.sample_rate = sample_rate
        
        # WebRTC VAD (更准确的语音检测)
        self.vad = webrtcvad.Vad(2)  # 0-3, 3最严格
        
        # 重叠窗口参数
        self.window_size = int(0.025 * sample_rate)  # 25ms窗口
        self.hop_size = int(0.010 * sample_rate)     # 10ms跳跃
        
        # 自适应阈值
        self.noise_level = 0.01
        self.adaptation_rate = 0.01
        
        # 语音缓冲区
        self.audio_buffer = deque(maxlen=int(5 * sample_rate))  # 5秒缓冲
        self.speech_segments = []
        
    def webrtc_vad_detection(self, audio_chunk):
        """使用WebRTC VAD进行语音检测"""
        # 转换为16位整数
        audio_int16 = (audio_chunk * 32767).astype(np.int16)
        
        # WebRTC VAD需要特定长度的音频块
        frame_length = int(0.01 * self.sample_rate)  # 10ms
        
        speech_frames = 0
        total_frames = 0
        
        for i in range(0, len(audio_int16) - frame_length, frame_length):
            frame = audio_int16[i:i + frame_length].tobytes()
            if self.vad.is_speech(frame, self.sample_rate):
                speech_frames += 1
            total_frames += 1
        
        return speech_frames / total_frames > 0.3 if total_frames > 0 else False
    
    def adaptive_threshold_vad(self, audio_chunk):
        """自适应阈值语音检测"""
        rms = np.sqrt(np.mean(audio_chunk ** 2))
        
        # 更新噪音水平
        if rms < self.noise_level * 2:  # 假设是噪音
            self.noise_level = (1 - self.adaptation_rate) * self.noise_level + \
                              self.adaptation_rate * rms
        
        # 动态阈值
        threshold = self.noise_level * 3
        return rms > threshold
    
    def spectral_vad(self, audio_chunk):
        """基于频谱特征的语音检测"""
        # 计算频谱质心
        stft = librosa.stft(audio_chunk, n_fft=512, hop_length=256)
        spectral_centroids = librosa.feature.spectral_centroid(S=np.abs(stft))[0]
        
        # 计算零交叉率
        zcr = librosa.feature.zero_crossing_rate(audio_chunk)[0]
        
        # 组合特征判断
        return np.mean(spectral_centroids) > 1000 and np.mean(zcr) < 0.3
    
    def ensemble_vad(self, audio_chunk):
        """集成多种VAD方法"""
        webrtc_result = self.webrtc_vad_detection(audio_chunk)
        adaptive_result = self.adaptive_threshold_vad(audio_chunk)
        spectral_result = self.spectral_vad(audio_chunk)
        
        # 投票机制
        votes = sum([webrtc_result, adaptive_result, spectral_result])
        return votes >= 2
    
    def overlapping_window_processing(self, audio_stream):
        """重叠窗口处理，避免语音边界截断"""
        windows = []
        
        for i in range(0, len(audio_stream) - self.window_size, self.hop_size):
            window = audio_stream[i:i + self.window_size]
            
            # 应用汉宁窗减少边界效应
            windowed = window * np.hanning(len(window))
            windows.append(windowed)
        
        return windows
    
    def audio_preprocessing(self, audio_chunk):
        """音频预处理：降噪、归一化"""
        # 高通滤波去除低频噪音
        sos = scipy.signal.butter(5, 80, btype='high', fs=self.sample_rate, output='sos')
        filtered = scipy.signal.sosfilt(sos, audio_chunk)
        
        # 归一化
        if np.max(np.abs(filtered)) > 0:
            normalized = filtered / np.max(np.abs(filtered)) * 0.8
        else:
            normalized = filtered
        
        # 简单降噪（谱减法）
        denoised = self.spectral_subtraction(normalized)
        
        return denoised
    
    def spectral_subtraction(self, audio_chunk, alpha=2.0):
        """简单的谱减法降噪"""
        # 短时傅里叶变换
        stft = librosa.stft(audio_chunk, n_fft=512, hop_length=256)
        magnitude = np.abs(stft)
        phase = np.angle(stft)
        
        # 估计噪音谱（使用前几帧）
        noise_frames = min(5, magnitude.shape[1])
        noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
        
        # 谱减法
        enhanced_magnitude = magnitude - alpha * noise_spectrum
        enhanced_magnitude = np.maximum(enhanced_magnitude, 0.1 * magnitude)
        
        # 重构信号
        enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
        enhanced_audio = librosa.istft(enhanced_stft, hop_length=256)
        
        return enhanced_audio
    
    def smart_segmentation(self, audio_stream, vad_results):
        """智能语音分段"""
        segments = []
        current_segment = []
        silence_count = 0
        min_segment_length = int(0.5 * self.sample_rate)  # 最小0.5秒
        max_silence_length = int(0.8 * self.sample_rate)  # 最大0.8秒静音
        
        chunk_size = int(0.01 * self.sample_rate)  # 10ms块
        
        for i, is_speech in enumerate(vad_results):
            start_idx = i * chunk_size
            end_idx = min(start_idx + chunk_size, len(audio_stream))
            chunk = audio_stream[start_idx:end_idx]
            
            if is_speech:
                current_segment.extend(chunk)
                silence_count = 0
            else:
                silence_count += len(chunk)
                
                # 在语音段中添加少量静音
                if current_segment and silence_count < max_silence_length:
                    current_segment.extend(chunk)
                
                # 检查是否结束当前段
                elif current_segment and silence_count >= max_silence_length:
                    if len(current_segment) >= min_segment_length:
                        segments.append(np.array(current_segment))
                    current_segment = []
                    silence_count = 0
        
        # 处理最后一段
        if current_segment and len(current_segment) >= min_segment_length:
            segments.append(np.array(current_segment))
        
        return segments
    
    def streaming_processing(self, audio_chunk):
        """流式处理新音频块"""
        # 预处理
        processed_chunk = self.audio_preprocessing(audio_chunk)
        
        # 添加到缓冲区
        self.audio_buffer.extend(processed_chunk)
        
        # VAD检测
        is_speech = self.ensemble_vad(processed_chunk)
        
        # 检查是否有完整的语音段可以处理
        if len(self.audio_buffer) >= self.sample_rate:  # 1秒缓冲
            buffer_array = np.array(self.audio_buffer)
            
            # 重叠窗口处理
            windows = self.overlapping_window_processing(buffer_array)
            
            # 对每个窗口进行VAD
            vad_results = [self.ensemble_vad(window) for window in windows]
            
            # 智能分段
            segments = self.smart_segmentation(buffer_array, vad_results)
            
            if segments:
                # 清空已处理的部分缓冲区
                keep_length = int(0.2 * self.sample_rate)  # 保留0.2秒重叠
                self.audio_buffer = deque(list(self.audio_buffer)[-keep_length:], 
                                        maxlen=int(5 * self.sample_rate))
                
                return segments
        
        return []

# 使用示例
def example_usage():
    processor = AdvancedSpeechProcessor()
    
    # 模拟音频流处理
    sample_rate = 16000
    duration = 0.1  # 100ms块
    
    while True:  # 实际应用中从麦克风获取
        # 模拟音频块
        audio_chunk = np.random.randn(int(duration * sample_rate)) * 0.1
        
        # 流式处理
        speech_segments = processor.streaming_processing(audio_chunk)
        
        # 处理检测到的语音段
        for segment in speech_segments:
            print(f"检测到语音段，长度: {len(segment)/sample_rate:.2f}秒")
            # 这里可以送入语音识别模型

if __name__ == "__main__":
    example_usage()
