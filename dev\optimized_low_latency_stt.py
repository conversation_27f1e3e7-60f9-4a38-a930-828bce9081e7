#!/usr/bin/env python3
"""
优化版低延迟语音识别系统
集成高级语音块处理技术
"""

import pyaudio
import numpy as np
import threading
import time
from faster_whisper import WhisperModel
from collections import deque
import queue
import torch
import webrtcvad
import scipy.signal

class OptimizedLowLatencySTT:
    def __init__(self, model_path: str):
        """初始化优化版STT系统"""
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paFloat32
        
        # 高级VAD参数
        self.vad = webrtcvad.Vad(2)  # WebRTC VAD
        self.noise_level = 0.01
        self.adaptation_rate = 0.01
        
        # 语音处理参数
        self.min_speech_duration = 0.3  # 最小语音时长
        self.max_silence_duration = 0.6  # 最大静音时长
        self.overlap_duration = 0.1     # 重叠时长
        
        # 检查GPU并初始化模型
        if torch.cuda.is_available():
            device = "cuda"
            compute_type = "float16"
            print(f"使用GPU加速: {torch.cuda.get_device_name()}")
        else:
            device = "cpu"
            compute_type = "int8"
            print("使用CPU模式")
        
        # 初始化组件
        self.audio = pyaudio.PyAudio()
        self.model = WhisperModel(
            model_path, 
            device=device, 
            compute_type=compute_type,
            num_workers=1
        )
        
        # 状态控制
        self.is_running = False
        self.audio_queue = queue.Queue()
        self.result_callback = None
        
        # 音频缓冲区
        self.audio_buffer = deque(maxlen=int(5 * self.sample_rate))
        self.speech_buffer = deque()
        self.last_speech_time = 0
        
        print("模型加载完成")

    def _preprocess_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """音频预处理：降噪和归一化"""
        # 高通滤波去除低频噪音
        sos = scipy.signal.butter(3, 80, btype='high', fs=self.sample_rate, output='sos')
        filtered = scipy.signal.sosfilt(sos, audio_data)
        
        # 归一化
        if np.max(np.abs(filtered)) > 0:
            normalized = filtered / np.max(np.abs(filtered)) * 0.8
        else:
            normalized = filtered
        
        return normalized

    def _webrtc_vad(self, audio_chunk: np.ndarray) -> bool:
        """WebRTC VAD检测"""
        try:
            # 转换为16位整数
            audio_int16 = (audio_chunk * 32767).astype(np.int16)
            
            # 10ms帧检测
            frame_length = int(0.01 * self.sample_rate)
            speech_frames = 0
            total_frames = 0
            
            for i in range(0, len(audio_int16) - frame_length, frame_length):
                frame = audio_int16[i:i + frame_length].tobytes()
                if self.vad.is_speech(frame, self.sample_rate):
                    speech_frames += 1
                total_frames += 1
            
            return speech_frames / total_frames > 0.3 if total_frames > 0 else False
        except:
            return self._adaptive_vad(audio_chunk)

    def _adaptive_vad(self, audio_chunk: np.ndarray) -> bool:
        """自适应阈值VAD"""
        rms = np.sqrt(np.mean(audio_chunk ** 2))
        
        # 更新噪音水平
        if rms < self.noise_level * 2:
            self.noise_level = (1 - self.adaptation_rate) * self.noise_level + \
                              self.adaptation_rate * rms
        
        threshold = max(self.noise_level * 3, 0.005)
        return rms > threshold

    def _ensemble_vad(self, audio_chunk: np.ndarray) -> bool:
        """集成VAD方法"""
        webrtc_result = self._webrtc_vad(audio_chunk)
        adaptive_result = self._adaptive_vad(audio_chunk)
        
        # 简单投票
        return webrtc_result or adaptive_result

    def _audio_callback(self, in_data, _frame_count, _time_info, _status):
        """音频输入回调"""
        audio_data = np.frombuffer(in_data, dtype=np.float32)
        self.audio_queue.put(audio_data)
        return (None, pyaudio.paContinue)

    def _process_audio(self):
        """优化的音频处理线程"""
        silence_duration = 0
        speech_detected = False
        
        while self.is_running:
            try:
                # 获取音频数据
                audio_chunk = self.audio_queue.get(timeout=0.1)
                
                # 预处理
                processed_chunk = self._preprocess_audio(audio_chunk)
                
                # 添加到缓冲区
                self.audio_buffer.extend(processed_chunk)
                
                # VAD检测
                is_speech = self._ensemble_vad(processed_chunk)
                current_time = time.time()
                
                if is_speech:
                    # 检测到语音
                    if not speech_detected:
                        speech_detected = True
                        print("🎤 语音中...")
                        # 添加一些前置音频（避免截断）
                        overlap_samples = int(self.overlap_duration * self.sample_rate)
                        if len(self.audio_buffer) > overlap_samples:
                            self.speech_buffer.extend(list(self.audio_buffer)[-overlap_samples:])
                    
                    self.speech_buffer.extend(processed_chunk)
                    self.last_speech_time = current_time
                    silence_duration = 0
                    
                else:
                    # 静音状态
                    if speech_detected:
                        silence_duration = current_time - self.last_speech_time
                        
                        # 添加少量静音到语音缓冲区
                        if silence_duration < self.max_silence_duration:
                            self.speech_buffer.extend(processed_chunk)
                        
                        # 检查是否结束语音段
                        elif silence_duration >= self.max_silence_duration:
                            speech_duration = len(self.speech_buffer) / self.sample_rate
                            
                            if speech_duration >= self.min_speech_duration:
                                # 处理语音段
                                audio_data = np.array(self.speech_buffer)
                                self._transcribe_audio(audio_data)
                            
                            # 重置状态
                            self.speech_buffer.clear()
                            speech_detected = False
                            silence_duration = 0
                
                # 限制缓冲区大小
                if len(self.audio_buffer) > int(2 * self.sample_rate):
                    # 保留最近1秒的数据
                    keep_samples = int(1 * self.sample_rate)
                    self.audio_buffer = deque(list(self.audio_buffer)[-keep_samples:], 
                                            maxlen=int(5 * self.sample_rate))
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"音频处理错误: {e}")

    def _transcribe_audio(self, audio_data: np.ndarray):
        """转录音频数据"""
        try:
            start_time = time.time()
            
            # 最终预处理
            if len(audio_data) < int(0.1 * self.sample_rate):
                return  # 太短的音频跳过
            
            # 使用优化参数进行识别
            segments, _ = self.model.transcribe(
                audio_data,
                language="zh",
                beam_size=1,
                temperature=0.0,
                no_speech_threshold=0.6,
                condition_on_previous_text=False,
                without_timestamps=True,
                initial_prompt="以下是普通话的句子。",
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=300)
            )
            
            # 提取文本
            text = "".join(segment.text for segment in segments).strip()
            
            if text:
                elapsed = time.time() - start_time
                print(f"识别: {text} ({elapsed:.1f}s)")
                
                if self.result_callback:
                    self.result_callback(text)
                    
        except Exception as e:
            print(f"识别失败: {e}")

    def start(self, result_callback=None):
        """开始语音识别"""
        self.result_callback = result_callback
        self.is_running = True
        
        # 启动音频流
        self.stream = self.audio.open(
            format=self.format,
            channels=self.channels,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )
        
        # 启动处理线程
        self.process_thread = threading.Thread(target=self._process_audio, daemon=True)
        self.process_thread.start()
        
        self.stream.start_stream()
        print("开始语音识别 (Ctrl+C 停止)")

    def stop(self):
        """停止语音识别"""
        self.is_running = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        
        self.audio.terminate()
        print("语音识别已停止")

def main():
    """主函数"""
    model_path = r"D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478"
    
    # 创建优化版STT实例
    stt = OptimizedLowLatencySTT(model_path)
    
    def on_result(_text):
        pass  # 结果已在识别时显示
    
    try:
        stt.start(result_callback=on_result)
        
        while True:
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n正在停止...")
        stt.stop()

if __name__ == "__main__":
    main()
