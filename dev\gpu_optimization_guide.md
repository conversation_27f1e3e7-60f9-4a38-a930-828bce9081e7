# GPU加速优化指南

## 已实现的GPU优化

### 1. 自动GPU检测和配置
- 自动检测CUDA GPU可用性
- GPU模式：使用 `float16` 精度以获得最佳性能
- CPU模式：使用 `int8` 精度以减少内存使用

### 2. 模型优化参数
```python
WhisperModel(
    model_path,
    device="cuda",           # 明确指定GPU
    compute_type="float16",  # GPU优化精度
    num_workers=1,          # 减少内存使用
    local_files_only=True   # 避免网络下载延迟
)
```

### 3. 转录参数优化
```python
model.transcribe(
    audio_data,
    beam_size=1,                    # 最小beam size，最快速度
    temperature=0.0,                # 确定性输出
    condition_on_previous_text=False, # 不依赖上下文
    without_timestamps=True,        # 不生成时间戳
    vad_filter=True,               # 启用VAD过滤
    vad_parameters=dict(min_silence_duration_ms=500)
)
```

## 进一步优化建议

### 1. 硬件要求
- **推荐GPU**: RTX 3060 或更高 (8GB+ VRAM)
- **最低GPU**: GTX 1660 或更高 (6GB+ VRAM)
- **CUDA版本**: 11.8 或更高

### 2. 环境优化
```bash
# 安装CUDA优化版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装优化版本的faster-whisper
pip install faster-whisper[gpu]
```

### 3. 系统优化
- 关闭Windows Defender实时保护（临时）
- 设置高性能电源模式
- 确保GPU驱动程序最新

### 4. 模型选择建议
- **最快**: `base` 模型 (39MB, ~0.2s)
- **平衡**: `small` 模型 (244MB, ~0.5s)  
- **当前**: `large-v3` 模型 (1550MB, ~1.0s)
- **最准确**: `large-v3` 模型但速度较慢

### 5. 音频预处理优化
- 降低采样率到8kHz（牺牲质量换速度）
- 减少chunk_size以降低延迟
- 调整VAD阈值以减少误触发

## 性能监控

运行程序时会显示：
- GPU信息和内存大小
- CUDA版本
- 每次识别的耗时

## 故障排除

### GPU未被使用
1. 检查CUDA安装：`nvidia-smi`
2. 检查PyTorch CUDA支持：`python -c "import torch; print(torch.cuda.is_available())"`
3. 重新安装CUDA版本的PyTorch

### 内存不足
1. 减少 `num_workers` 到 1
2. 使用更小的模型
3. 增加系统虚拟内存

### 速度仍然慢
1. 尝试使用更小的模型
2. 调整音频参数（降低采样率）
3. 检查系统后台进程
