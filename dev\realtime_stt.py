#!/usr/bin/env python3
"""
低延迟实时语音识别示例程序

基于faster-whisper的高效实时语音转文本系统
特点：低延迟、高准确率、简洁易用

使用方法：
    python realtime_stt.py

控制：
    按 Enter 键停止识别
    Ctrl+C 退出程序
"""

import os
import sys
import time
import json
import logging
import threading
import tempfile
import numpy as np
import warnings
from typing import Optional, Dict, Any

# 抑制警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 检查依赖
try:
    from faster_whisper import WhisperModel
    import sounddevice as sd
    import soundfile as sf
except ImportError as e:
    print(f"❌ 缺少必要依赖: {e}")
    print("请运行: pip install faster-whisper sounddevice soundfile")
    sys.exit(1)


class SimpleWhisperClient:
    """简化的Whisper客户端"""

    def __init__(self, model_path: str):
        """初始化Whisper客户端"""
        self.model_path = model_path
        self.model = None
        self._load_model()

    def _load_model(self):
        """加载模型"""
        try:
            print(f"📥 加载Whisper模型: {self.model_path}")
            self.model = WhisperModel(
                self.model_path,
                device="auto",
                compute_type="float16"
            )
            print("✅ 模型加载成功")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise

    def transcribe(self, audio_data: np.ndarray) -> str:
        """转录音频"""
        try:
            segments, _ = self.model.transcribe(
                audio_data,
                language="zh",
                beam_size=1,
                temperature=0.0,
                no_speech_threshold=0.6,
                condition_on_previous_text=False,
                without_timestamps=True,
                initial_prompt="以下是普通话的句子。"
            )

            text = ""
            for segment in segments:
                text += segment.text

            return text.strip()
        except Exception as e:
            print(f"⚠️ 识别错误: {e}")
            return ""


class SimpleAudioRecorder:
    """简化的音频录制器"""

    def __init__(self, sample_rate: int = 16000, chunk_duration: float = 0.5):
        """初始化录制器"""
        self.sample_rate = sample_rate
        self.chunk_duration = chunk_duration
        self.chunk_size = int(sample_rate * chunk_duration)
        self.is_recording = False
        self.audio_buffer = []
        self.silence_threshold = 0.01

        # 检查音频设备
        try:
            devices = sd.query_devices()
            input_devices = [d for d in devices if d['max_input_channels'] > 0]
            if not input_devices:
                raise Exception("未找到音频输入设备")
            print(f"🎤 找到 {len(input_devices)} 个音频输入设备")
        except Exception as e:
            print(f"❌ 音频设备检查失败: {e}")
            raise

    def record_chunk(self) -> Optional[np.ndarray]:
        """录制一个音频块"""
        try:
            audio_data = sd.rec(
                self.chunk_size,
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            sd.wait()  # 等待录制完成
            return audio_data.flatten()
        except Exception as e:
            print(f"⚠️ 录制错误: {e}")
            return None

    def is_speech(self, audio_chunk: np.ndarray) -> bool:
        """简单的语音检测"""
        rms = np.sqrt(np.mean(audio_chunk ** 2))
        return rms > self.silence_threshold


class RealTimeSTT:
    """实时语音识别器"""
    
    def __init__(self):
        """初始化实时STT系统"""
        print("🚀 初始化低延迟语音识别系统...")
        
        # 优化配置：针对低延迟
        self.config = {
            "model_config": {
                "model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3/snapshots/edaa852ec7e145841d8ffdb056a99866b5f0a478",
                "device": "auto",
                "compute_type": "float16"
            },
            "audio_config": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_duration": 0.5,  # 0.5秒块，降低延迟
                "max_duration": 10.0,   # 单次最大10秒
                "silence_threshold": 0.01,
                "silence_duration": 1.0  # 1秒静音后停止
            },
            "vad_config": {
                "enabled": True,
                "threshold": 0.5,
                "min_speech_duration": 0.3,  # 最小语音时长
                "min_silence_duration": 0.8  # 最小静音时长
            }
        }
        
        # 初始化组件
        self.whisper_client = None
        self.audio_recorder = None
        self.is_running = False
        self.recognition_thread = None
        
        # 初始化STT组件
        self._initialize_components()
    
    def _initialize_components(self) -> bool:
        """初始化STT组件"""
        try:
            # 创建临时配置文件
            import json
            import tempfile
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(self.config, f, indent=2)
                config_path = f.name
            
            # 初始化Whisper客户端（预加载模型）
            print("📥 加载Whisper模型...")
            self.whisper_client = WhisperClient(config_path, preload_model=True)
            
            # 初始化音频录制器
            print("🎤 初始化音频录制器...")
            self.audio_recorder = AudioRecorder(config_path)
            
            # 清理临时文件
            os.unlink(config_path)
            
            print("✅ STT系统初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    def _process_audio_chunk(self, audio_data: np.ndarray) -> Optional[str]:
        """处理音频块并返回识别结果"""
        if len(audio_data) < 8000:  # 少于0.5秒的音频跳过
            return None
        
        try:
            # 快速语音识别
            text = self.whisper_client.transcribe(audio_data, language="zh")
            return text.strip() if text else None
        except Exception as e:
            print(f"⚠️ 识别错误: {e}")
            return None
    
    def start_recognition(self):
        """开始实时语音识别"""
        if not self.whisper_client or not self.audio_recorder:
            print("❌ STT系统未正确初始化")
            return
        
        self.is_running = True
        print("\n🎤 开始实时语音识别...")
        print("💡 说话时会自动识别，静音1秒后显示结果")
        print("📝 按 Enter 键停止识别\n")
        
        # 设置音频回调
        speech_buffer = []
        last_speech_time = 0
        
        def on_audio_chunk(audio_chunk: np.ndarray):
            """音频块处理回调"""
            nonlocal speech_buffer, last_speech_time
            
            # 检测是否有语音
            rms = np.sqrt(np.mean(audio_chunk ** 2))
            is_speech = rms > self.config["audio_config"]["silence_threshold"]
            
            if is_speech:
                speech_buffer.append(audio_chunk)
                last_speech_time = time.time()
                if len(speech_buffer) == 1:  # 语音开始
                    print("🗣️ 检测到语音...", end="", flush=True)
            else:
                # 检查是否需要处理累积的语音
                if speech_buffer and (time.time() - last_speech_time) > self.config["vad_config"]["min_silence_duration"]:
                    # 合并语音数据并识别
                    if len(speech_buffer) > 0:
                        audio_data = np.concatenate(speech_buffer)
                        speech_buffer = []
                        
                        # 异步处理识别
                        def recognize():
                            text = self._process_audio_chunk(audio_data)
                            if text:
                                print(f"\r📝 识别结果: {text}")
                            else:
                                print("\r🔇 未识别到有效语音")
                            print("🎤 继续监听...", end="", flush=True)
                        
                        threading.Thread(target=recognize, daemon=True).start()
        
        # 设置回调并开始录制
        self.audio_recorder.on_audio_chunk = on_audio_chunk
        
        # 启动录制线程
        def recording_loop():
            """录制循环"""
            while self.is_running:
                try:
                    # 连续录制小块音频
                    self.audio_recorder.record_continuous(
                        duration=self.config["audio_config"]["chunk_duration"]
                    )
                    time.sleep(0.1)  # 短暂休息
                except Exception as e:
                    print(f"\n⚠️ 录制错误: {e}")
                    break
        
        self.recognition_thread = threading.Thread(target=recording_loop, daemon=True)
        self.recognition_thread.start()
        
        # 等待用户输入停止
        try:
            input()  # 等待Enter键
        except KeyboardInterrupt:
            pass
        
        self.stop_recognition()
    
    def stop_recognition(self):
        """停止语音识别"""
        self.is_running = False
        if self.audio_recorder:
            self.audio_recorder.stop_recording()
        
        print("\n🛑 语音识别已停止")
    
    def test_system(self) -> bool:
        """测试系统功能"""
        print("🧪 测试STT系统...")
        
        # 测试Whisper模型
        if not self.whisper_client.test_model():
            print("❌ Whisper模型测试失败")
            return False
        
        # 测试音频录制
        if not self.audio_recorder.test_recording(2.0):
            print("❌ 音频录制测试失败")
            return False
        
        print("✅ 系统测试通过")
        return True


def main():
    """主函数"""
    print("=" * 50)
    print("🎯 低延迟实时语音识别系统")
    print("=" * 50)
    
    try:
        # 创建STT系统
        stt_system = RealTimeSTT()
        
        # 系统测试
        if not stt_system.test_system():
            print("❌ 系统测试失败，请检查配置")
            return
        
        # 开始实时识别
        stt_system.start_recognition()
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🔚 程序结束")


if __name__ == "__main__":
    main()
