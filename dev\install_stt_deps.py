#!/usr/bin/env python3
"""
STT依赖安装脚本

自动检测并安装实时语音识别所需的依赖库
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("🔧 检查STT依赖...")
    
    # 必需的依赖包
    dependencies = [
        ("faster-whisper", "faster_whisper"),
        ("sounddevice", "sounddevice"),
        ("soundfile", "soundfile"),
        ("numpy", "numpy"),
    ]
    
    # 可选依赖
    optional_deps = [
        ("webrtcvad", "webrtcvad"),
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    # 检查必需依赖
    for package, import_name in dependencies:
        if check_and_install_package(package, import_name):
            success_count += 1
    
    # 检查可选依赖
    for package, import_name in optional_deps:
        check_and_install_package(package, import_name)
    
    print(f"\n📊 依赖检查结果: {success_count}/{total_count} 必需依赖已安装")
    
    if success_count == total_count:
        print("🎉 所有依赖已就绪，可以运行实时STT程序！")
        return True
    else:
        print("⚠️ 部分依赖安装失败，请手动安装")
        return False

if __name__ == "__main__":
    main()
